import 'package:flutter_test/flutter_test.dart';
import 'package:easydine_main/services/table_reservation_service.dart';
import 'package:easydine_main/models/table_reservation_model.dart';

void main() {
  group('TableReservationService', () {
    test('CreateReservationRequest should serialize correctly', () {
      final request = CreateReservationRequest(
        customerName: '<PERSON>',
        phoneNumber: '+1234567890',
        numberOfGuests: 4,
        reservationTime: DateTime(2025, 7, 1, 16, 10, 4),
        specialNotes: 'Birthday celebration',
      );

      final json = request.toJson();

      expect(json['customerName'], equals('<PERSON>'));
      expect(json['phoneNumber'], equals('+1234567890'));
      expect(json['numberOfGuests'], equals(4));
      expect(json['reservationTime'], equals('2025-07-01T16:10:04.000'));
      expect(json['specialNotes'], equals('Birthday celebration'));
    });

    test('CreateReservationRequest should handle null special notes', () {
      final request = CreateReservationRequest(
        customerName: '<PERSON>',
        phoneNumber: '+1234567890',
        numberOfGuests: 2,
        reservationTime: DateTime(2025, 7, 1, 18, 30, 0),
      );

      final json = request.toJson();

      expect(json['customerName'], equals('Jane Doe'));
      expect(json['phoneNumber'], equals('+1234567890'));
      expect(json['numberOfGuests'], equals(2));
      expect(json['reservationTime'], equals('2025-07-01T18:30:00.000'));
      expect(json.containsKey('specialNotes'), isFalse);
    });

    test('TableReservationModel should parse from JSON correctly', () {
      final json = {
        'reservationId': 'res-123',
        'tableId': 'table-456',
        'customerName': 'John Doe',
        'phoneNumber': '+1234567890',
        'numberOfGuests': 4,
        'reservationTime': '2025-07-01T16:10:04.000Z',
        'specialNotes': 'Birthday celebration',
        'status': 'confirmed',
        'createdAt': '2025-07-01T10:00:00.000Z',
        'updatedAt': '2025-07-01T10:00:00.000Z',
        'checkedInAt': null,
        'cancelledAt': null,
      };

      final reservation = TableReservationModel.fromJson(json);

      expect(reservation.reservationId, equals('res-123'));
      expect(reservation.tableId, equals('table-456'));
      expect(reservation.customerName, equals('John Doe'));
      expect(reservation.phoneNumber, equals('+1234567890'));
      expect(reservation.numberOfGuests, equals(4));
      expect(reservation.specialNotes, equals('Birthday celebration'));
      expect(reservation.status, equals('confirmed'));
      expect(reservation.checkedInAt, isNull);
      expect(reservation.cancelledAt, isNull);
    });

    test('ReservationStatus extension should work correctly', () {
      expect(ReservationStatus.pending.value, equals('pending'));
      expect(ReservationStatus.confirmed.value, equals('confirmed'));
      expect(ReservationStatus.checkedIn.value, equals('checked_in'));
      expect(ReservationStatus.cancelled.value, equals('cancelled'));
      expect(ReservationStatus.noShow.value, equals('no_show'));

      expect(ReservationStatusExtension.fromString('pending'), 
             equals(ReservationStatus.pending));
      expect(ReservationStatusExtension.fromString('confirmed'), 
             equals(ReservationStatus.confirmed));
      expect(ReservationStatusExtension.fromString('checked_in'), 
             equals(ReservationStatus.checkedIn));
      expect(ReservationStatusExtension.fromString('cancelled'), 
             equals(ReservationStatus.cancelled));
      expect(ReservationStatusExtension.fromString('no_show'), 
             equals(ReservationStatus.noShow));
      expect(ReservationStatusExtension.fromString('unknown'), 
             equals(ReservationStatus.pending));
    });

    test('UpdateReservationRequest should only include non-null fields', () {
      final request = UpdateReservationRequest(
        customerName: 'Updated Name',
        numberOfGuests: 6,
        // phoneNumber and reservationTime are null
      );

      final json = request.toJson();

      expect(json['customerName'], equals('Updated Name'));
      expect(json['numberOfGuests'], equals(6));
      expect(json.containsKey('phoneNumber'), isFalse);
      expect(json.containsKey('reservationTime'), isFalse);
      expect(json.containsKey('specialNotes'), isFalse);
    });
  });
}
