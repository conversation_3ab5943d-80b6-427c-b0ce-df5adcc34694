import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:easydine_main/blocs/cart/cart_bloc.dart';
import 'package:easydine_main/blocs/cart/cart_event.dart';
import 'package:easydine_main/blocs/cart/cart_state.dart';
import 'package:easydine_main/models/cart_models.dart';
import 'package:easydine_main/models/cartItem.dart';

void main() {
  group('CartBloc Tests', () {
    late CartBloc cartBloc;

    setUp(() {
      cartBloc = CartBloc();
    });

    tearDown(() {
      cartBloc.close();
    });

    test('initial state is CartInitial', () {
      expect(cartBloc.state, equals(const CartInitial()));
    });

    group('LoadCart', () {
      blocTest<CartBloc, CartState>(
        'emits [CartLoading, CartError] when cart loading fails',
        build: () => cartBloc,
        act: (bloc) => bloc.add(const LoadCart()),
        expect: () => [
          const CartLoading(),
          isA<CartError>(),
        ],
      );
    });

    group('AddItemToCart', () {
      blocTest<CartBloc, CartState>(
        'emits [CartProcessing, CartError] when adding item fails',
        build: () => cartBloc,
        act: (bloc) => bloc.add(AddItemToCart(
          request: const AddItemToCartRequest(
            quantity: 1,
            dishId: 'test-dish-id',
          ),
        )),
        expect: () => [
          isA<CartProcessing>(),
          isA<CartError>(),
        ],
      );
    });

    group('ClearCart', () {
      blocTest<CartBloc, CartState>(
        'emits [CartProcessing, CartError] when clearing cart fails',
        build: () => cartBloc,
        act: (bloc) => bloc.add(const ClearCart()),
        expect: () => [
          isA<CartProcessing>(),
          isA<CartError>(),
        ],
      );
    });

    group('SetCartError', () {
      blocTest<CartBloc, CartState>(
        'emits state with error message',
        build: () => cartBloc,
        act: (bloc) => bloc.add(const SetCartError(error: 'Test error')),
        expect: () => [
          isA<CartState>()
              .having((state) => state.error, 'error', 'Test error'),
        ],
      );
    });

    group('ClearCartError', () {
      blocTest<CartBloc, CartState>(
        'emits state with cleared error',
        build: () => cartBloc,
        seed: () => const CartError(error: 'Test error'),
        act: (bloc) => bloc.add(const ClearCartError()),
        expect: () => [
          isA<CartState>().having((state) => state.error, 'error', null),
        ],
      );
    });
  });

  group('Cart Models Tests', () {
    test('AddItemToCartRequest toJson works correctly', () {
      const request = AddItemToCartRequest(
        quantity: 2,
        dishId: 'test-dish-id',
        type: 'customized',
        notes: 'Extra spicy',
        allergyIds: ['allergy-1'],
        dishAddons: [DishAddon(id: 'addon-1', quantity: 1)],
      );

      final json = request.toJson();

      expect(json['quantity'], 2);
      expect(json['dishId'], 'test-dish-id');
      expect(json['type'], 'customized');
      expect(json['notes'], 'Extra spicy');
      expect(json['allergyIds'], ['allergy-1']);
      expect(json['dishAddons'], [
        {'id': 'addon-1', 'quantity': 1}
      ]);
    });

    test('DishAddon fromJson and toJson work correctly', () {
      const addon = DishAddon(id: 'addon-1', quantity: 2);
      final json = addon.toJson();
      final fromJson = DishAddon.fromJson(json);

      expect(fromJson.id, addon.id);
      expect(fromJson.quantity, addon.quantity);
    });

    test('ConfirmCartRequest toJson works correctly', () {
      const request = ConfirmCartRequest(
        assignedWaiterId: 'waiter-1',
        tableId: 'table-1',
        orderTypeId: 'order-type-1',
      );

      final json = request.toJson();

      expect(json['assignedWaiterId'], 'waiter-1');
      expect(json['tableId'], 'table-1');
      expect(json['orderTypeId'], 'order-type-1');
    });
  });

  group('CartState Tests', () {
    test('CartState getters work correctly', () {
      final cart = Cart(
        cartId: 'cart-1',
        staffId: 'staff-1',
        status: 'ACTIVE',
        branch: const CartBranch(id: 'branch-1', name: 'Test Branch'),
        total: '25.50',
        items: const [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final state = CartLoaded(cart: cart);

      expect(state.total, 25.50);
      expect(state.itemCount, 0);
      expect(state.isEmpty, true);
      expect(state.hasItems, false);
      expect(state.cartStatus, 'ACTIVE');
      expect(state.isActive, true);
      expect(state.isOnHold, false);
      expect(state.isReady, true);
    });

    test('CartState copyWith works correctly', () {
      const initialState = CartInitial();
      const error = 'Test error';

      final newState = initialState.copyWith(error: error);

      expect(newState.error, error);
      expect(newState.status, CartStatus.initial);
    });

    test('CartState copyWith with clearError works correctly', () {
      const errorState = CartError(error: 'Test error');

      final clearedState = errorState.copyWith(clearError: true);

      expect(clearedState.error, null);
    });
  });
}
