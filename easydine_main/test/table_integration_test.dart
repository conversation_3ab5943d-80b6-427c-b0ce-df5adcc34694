import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:easydine_main/blocs/table/table_bloc.dart';
import 'package:easydine_main/blocs/table/table_event.dart';
import 'package:easydine_main/blocs/table/table_state.dart';
import 'package:easydine_main/blocs/running_orders/running_orders_bloc.dart';
import 'package:easydine_main/models/floor_model.dart';
import 'package:easydine_main/models/table_api_model.dart';
import 'package:easydine_main/models/table_combination_model.dart';

// Mock classes
class MockRunningOrdersBloc extends Mock implements RunningOrdersBloc {}

void main() {
  group('Table Integration Tests', () {
    late TableBloc tableBloc;
    late MockRunningOrdersBloc mockRunningOrdersBloc;

    setUp(() {
      mockRunningOrdersBloc = MockRunningOrdersBloc();
      tableBloc = TableBloc(runningOrdersBloc: mockRunningOrdersBloc);
    });

    tearDown(() {
      tableBloc.close();
    });

    test('initial state is correct', () {
      expect(tableBloc.state, const TableState());
    });

    group('LoadFloorsAndTables', () {
      blocTest<TableBloc, TableState>(
        'emits loading state then success state with mock data when API fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(LoadFloorsAndTables()),
        expect: () => [
          const TableState(isLoading: true),
          isA<TableState>()
              .having((state) => state.isLoading, 'isLoading', false)
              .having((state) => state.tables.isNotEmpty, 'has tables', true)
              .having((state) => state.error, 'error', contains('offline data')),
        ],
      );
    });

    group('Table Status Updates', () {
      blocTest<TableBloc, TableState>(
        'UpdateTableStatus emits error when API call fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(const UpdateTableStatus(
          tableId: 'test-table-id',
          newStatus: 'occupied',
        )),
        expect: () => [
          isA<TableState>()
              .having((state) => state.error, 'error', isNotNull),
        ],
      );

      blocTest<TableBloc, TableState>(
        'UpdateTableCleaningStatus emits error when API call fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(const UpdateTableCleaningStatus(
          tableId: 'test-table-id',
          newStatus: 'clean',
        )),
        expect: () => [
          isA<TableState>()
              .having((state) => state.error, 'error', isNotNull),
        ],
      );

      blocTest<TableBloc, TableState>(
        'UpdateTableBookedSeats emits error when API call fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(const UpdateTableBookedSeats(
          tableId: 'test-table-id',
          bookedSeats: 4,
        )),
        expect: () => [
          isA<TableState>()
              .having((state) => state.error, 'error', isNotNull),
        ],
      );
    });

    group('Table Actions', () {
      blocTest<TableBloc, TableState>(
        'ReserveTable emits error when API call fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(const ReserveTable(
          tableId: 'test-table-id',
          bookedSeats: 2,
        )),
        expect: () => [
          isA<TableState>()
              .having((state) => state.error, 'error', isNotNull),
        ],
      );

      blocTest<TableBloc, TableState>(
        'MarkTableAsOccupied emits error when API call fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(const MarkTableAsOccupied(
          tableId: 'test-table-id',
          bookedSeats: 4,
        )),
        expect: () => [
          isA<TableState>()
              .having((state) => state.error, 'error', isNotNull),
        ],
      );

      blocTest<TableBloc, TableState>(
        'ResetTable emits error when API call fails',
        build: () => tableBloc,
        act: (bloc) => bloc.add(const ResetTable(
          tableId: 'test-table-id',
        )),
        expect: () => [
          isA<TableState>()
              .having((state) => state.error, 'error', isNotNull),
        ],
      );
    });

    group('Model Conversions', () {
      test('TableApiModel can be created from JSON', () {
        final json = {
          'tableId': 'test-id',
          'name': 'T1',
          'minSeats': 1,
          'maxSeats': 4,
          'bookedSeats': 0,
          'enabled': true,
          'status': 'available',
          'cleaning': 'clean',
          'location': 'Main Area',
          'availableOnline': true,
          'createdAt': '2025-01-01T00:00:00.000Z',
          'updatedAt': '2025-01-01T00:00:00.000Z',
          'deletedAt': null,
          'availableSeats': 4,
          'reservedSeats': 0,
          'hasReservations': false,
          'reservationCount': 0,
          'todaysReservations': [],
          'displayStatus': 'AVAILABLE',
          'isFullyOccupied': false,
          'isPartiallyOccupied': false,
          'isEmpty': true,
          'needsCleaning': false,
          'lastUpdated': '2025-01-01T00:00:00.000Z',
          'availabilitySummary': {
            'status': 'available',
            'bookedSeats': 0,
            'availableSeats': 4,
            'maxSeats': 4,
            'cleaningStatus': 'clean',
            'isEnabled': true,
          },
        };

        final table = TableApiModel.fromJson(json);
        expect(table.tableId, 'test-id');
        expect(table.name, 'T1');
        expect(table.maxSeats, 4);
        expect(table.status, 'available');
      });

      test('FloorModel can be created from JSON', () {
        final json = {
          'floorId': 'floor-1',
          'name': 'Floor 1',
          'createdAt': '2025-01-01T00:00:00.000Z',
          'updatedAt': '2025-01-01T00:00:00.000Z',
          'deletedAt': null,
          'tables': [],
          'tableCombinations': [],
        };

        final floor = FloorModel.fromJson(json);
        expect(floor.floorId, 'floor-1');
        expect(floor.name, 'Floor 1');
        expect(floor.tables, isEmpty);
        expect(floor.tableCombinations, isEmpty);
      });
    });

    group('Legacy Compatibility', () {
      test('TableModel helper methods work correctly', () {
        // This would require creating a TableModel instance
        // but since we're testing the integration, we'll focus on the API models
        expect(true, true); // Placeholder test
      });
    });
  });
}
