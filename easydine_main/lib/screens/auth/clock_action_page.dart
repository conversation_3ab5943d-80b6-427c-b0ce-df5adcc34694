import 'package:easydine_main/blocs/staff/staff_bloc.dart';
import 'package:easydine_main/blocs/staff/staff_event.dart';
import 'package:easydine_main/blocs/staff/staff_state.dart';
import 'package:easydine_main/blocs/attendance/attendance_bloc.dart';
import 'package:easydine_main/blocs/attendance/attendance_event.dart';
import 'package:easydine_main/blocs/attendance/attendance_state.dart';
import 'package:easydine_main/models/staff_model.dart';
import 'package:easydine_main/services/branch_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

enum ClockAction { clockIn, clockOut }

class ClockActionPage extends StatefulWidget {
  final ClockAction action;

  const ClockActionPage({
    super.key,
    required this.action,
  });

  @override
  State<ClockActionPage> createState() => _ClockActionPageState();
}

class _ClockActionPageState extends State<ClockActionPage>
    with TickerProviderStateMixin {
  String _pin = '';
  bool _showError = false;
  StaffModel? _selectedStaff;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
    context.read<AttendanceBloc>().add(const ResetAttendanceState());
    _fetchStaffData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onKeyPress(String digit) {
    if (_pin.length < 5) {
      setState(() {
        _pin += digit;
        _showError = false;
      });

      if (_pin.length == 5 && _selectedStaff != null) {
        if (widget.action == ClockAction.clockIn) {
          context.read<AttendanceBloc>().add(
                ClockInStaff(
                  pin: _pin,
                  staffId: _selectedStaff!.id,
                ),
              );
        } else {
          context.read<AttendanceBloc>().add(
                ClockOutStaff(
                  pin: _pin,
                  staffId: _selectedStaff!.id,
                ),
              );
        }
      }
    }
  }

  void _onBackspace() {
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
        _showError = false;
      });
    }
  }

  Future<void> _fetchStaffData() async {
    final branchId = await BranchService.getSelectedBranchId();
    if (branchId != null && mounted) {
      if (widget.action == ClockAction.clockOut) {
        context.read<StaffBloc>().add(FetchClockedInStaff(branchId: branchId));
      } else {
        context.read<StaffBloc>().add(FetchBranchStaff(branchId: branchId));
      }
    }
  }

  // New widget components for the redesigned page
  Widget _buildTimeTrackingHeader() {
    final isClockIn = widget.action == ClockAction.clockIn;
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isClockIn
                      ? [
                          const Color(0xFF4CAF50),
                          const Color(0xFF2E7D32),
                        ]
                      : [
                          const Color(0xFFFF9800),
                          const Color(0xFFE65100),
                        ],
                ),
                borderRadius: BorderRadius.circular(3.w),
                boxShadow: [
                  BoxShadow(
                    color: (isClockIn ? Colors.green : Colors.orange)
                        .withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.all(2.w),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isClockIn ? Icons.access_time : Icons.timer_off,
                          size: 8.w,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        isClockIn ? 'TIME TRACKING' : 'END SHIFT',
                        style: GoogleFonts.poppins(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 2,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    isClockIn ? 'Start Your Work Day' : 'Complete Your Shift',
                    style: GoogleFonts.poppins(
                      fontSize: 14.sp,
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStaffGrid() {
    return BlocBuilder<StaffBloc, StaffState>(
      builder: (context, staffState) {
        final filteredStaffList = widget.action == ClockAction.clockOut
            ? staffState.clockedInStaff
            : staffState.allStaff;

        if (staffState.status == StaffStatus.loading) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.action == ClockAction.clockIn
                    ? Colors.green
                    : Colors.orange,
              ),
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              child: Text(
                widget.action == ClockAction.clockOut
                    ? 'Select Staff to Clock Out'
                    : 'Select Staff to Clock In',
                style: GoogleFonts.poppins(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount:
                      MediaQuery.of(context).orientation == Orientation.portrait
                          ? 2
                          : 3,
                  childAspectRatio:
                      MediaQuery.of(context).orientation == Orientation.portrait
                          ? 1.4
                          : 1.2,
                  crossAxisSpacing: 2.w,
                  mainAxisSpacing: 1.5.h,
                ),
                itemCount: filteredStaffList.length,
                itemBuilder: (context, index) {
                  final staff = filteredStaffList[index];
                  final isSelected = _selectedStaff?.id == staff.id;
                  final isClockIn = widget.action == ClockAction.clockIn;

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isClockIn
                                  ? [
                                      Colors.green.shade400,
                                      Colors.green.shade600
                                    ]
                                  : [
                                      Colors.orange.shade400,
                                      Colors.orange.shade600
                                    ],
                            )
                          : LinearGradient(
                              colors: [
                                Colors.grey.shade800,
                                Colors.grey.shade900,
                              ],
                            ),
                      borderRadius: BorderRadius.circular(3.w),
                      border: Border.all(
                        color: isSelected
                            ? Colors.white.withValues(alpha: 0.5)
                            : Colors.grey.withValues(alpha: 0.3),
                        width: 2,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color:
                                    (isClockIn ? Colors.green : Colors.orange)
                                        .withValues(alpha: 0.4),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ]
                          : [],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(3.w),
                        onTap: () {
                          setState(() {
                            _selectedStaff = staff;
                            _pin = '';
                            _showError = false;
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.all(2.w),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircleAvatar(
                                radius: 5.w,
                                backgroundColor: isSelected
                                    ? Colors.white.withValues(alpha: 0.2)
                                    : Colors.grey.withValues(alpha: 0.3),
                                child: Icon(
                                  Icons.person,
                                  size: 5.w,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(height: 0.8.h),
                              Flexible(
                                child: Text(
                                  staff.name,
                                  style: GoogleFonts.poppins(
                                    fontSize: 11.sp,
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.w500,
                                    color: Colors.white,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(height: 0.3.h),
                              Flexible(
                                child: Text(
                                  staff.role ?? 'Staff',
                                  style: GoogleFonts.poppins(
                                    fontSize: 9.sp,
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (widget.action == ClockAction.clockOut)
                                Container(
                                  margin: EdgeInsets.only(top: 0.3.h),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 1.5.w,
                                    vertical: 0.3.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(1.w),
                                  ),
                                  child: Text(
                                    'Active',
                                    style: GoogleFonts.poppins(
                                      fontSize: 7.sp,
                                      color: Colors.green.shade300,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPinEntrySection() {
    final isClockIn = widget.action == ClockAction.clockIn;

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            padding: EdgeInsets.all(4.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Status indicator
                Container(
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isClockIn
                          ? [Colors.green.shade100, Colors.green.shade50]
                          : [Colors.orange.shade100, Colors.orange.shade50],
                    ),
                    borderRadius: BorderRadius.circular(4.w),
                    border: Border.all(
                      color: isClockIn
                          ? Colors.green.shade300
                          : Colors.orange.shade300,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        isClockIn ? Icons.schedule : Icons.schedule_outlined,
                        size: 12.w,
                        color: isClockIn
                            ? Colors.green.shade700
                            : Colors.orange.shade700,
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        _selectedStaff == null
                            ? 'Select Staff Member Above'
                            : '${isClockIn ? 'Clock In' : 'Clock Out'} - ${_selectedStaff!.name}',
                        style: GoogleFonts.poppins(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: isClockIn
                              ? Colors.green.shade800
                              : Colors.orange.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (_selectedStaff != null) ...[
                        SizedBox(height: 1.h),
                        Text(
                          'Enter your 5-digit PIN',
                          style: GoogleFonts.poppins(
                            fontSize: 12.sp,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                SizedBox(height: 4.h),

                // PIN dots
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    5,
                    (index) => Container(
                      margin: EdgeInsets.symmetric(horizontal: 1.w),
                      width: 4.w,
                      height: 4.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index < _pin.length
                            ? (isClockIn ? Colors.green : Colors.orange)
                            : Colors.grey.shade300,
                        border: Border.all(
                          color: index < _pin.length
                              ? (isClockIn
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700)
                              : Colors.grey.shade400,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ),

                if (_showError) ...[
                  SizedBox(height: 2.h),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(2.w),
                      border: Border.all(color: Colors.red.shade300),
                    ),
                    child: Text(
                      'Invalid PIN. Please try again.',
                      style: GoogleFonts.poppins(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                ],

                SizedBox(height: 4.h),

                // Keypad
                Container(
                  constraints: BoxConstraints(maxWidth: 80.w),
                  child: GridView.count(
                    shrinkWrap: true,
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    mainAxisSpacing: 2.h,
                    crossAxisSpacing: 3.w,
                    children: [
                      for (var i = 1; i <= 9; i++)
                        _buildKeypadButton(i.toString()),
                      _buildKeypadButton(''),
                      _buildKeypadButton('0'),
                      _buildKeypadButton('⌫'),
                    ],
                  ),
                ),

                SizedBox(height: 4.h),

                // Cancel button
                TextButton.icon(
                  onPressed: () => context.pop(),
                  icon: Icon(
                    Icons.arrow_back,
                    color: Colors.grey.shade600,
                  ),
                  label: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      color: Colors.grey.shade600,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildKeypadButton(String value) {
    return Container(
      decoration: BoxDecoration(
        gradient: value.isEmpty
            ? null
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  Colors.grey.shade50,
                ],
              ),
        borderRadius: BorderRadius.circular(3.w),
        border: value.isEmpty
            ? null
            : Border.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
        boxShadow: value.isEmpty
            ? null
            : [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(3.w),
          onTap: _selectedStaff == null || value.isEmpty
              ? null
              : () {
                  if (value == '⌫') {
                    _onBackspace();
                  } else {
                    _onKeyPress(value);
                  }
                },
          child: Container(
            alignment: Alignment.center,
            child: value == '⌫'
                ? Icon(
                    Icons.backspace_outlined,
                    color: Colors.grey.shade600,
                    size: 6.w,
                  )
                : Text(
                    value,
                    style: GoogleFonts.poppins(
                      fontSize: 18.sp,
                      color: Colors.grey.shade800,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AttendanceBloc, AttendanceState>(
      listenWhen: (previous, current) => previous.status != current.status,
      listener: (context, state) {
        if (state.status == AttendanceStatus.success) {
          // Pop the current page and return to pin entry
          context.pop();

          // Show a brief success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${_selectedStaff?.name} has ${widget.action == ClockAction.clockIn ? 'clocked in' : 'clocked out'} successfully',
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (state.status == AttendanceStatus.error) {
          setState(() {
            _pin = '';
            _showError = true;
          });

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.error ??
                    'Failed to ${widget.action == ClockAction.clockIn ? 'clock in' : 'clock out'}. Please try again.',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: BlocBuilder<AttendanceBloc, AttendanceState>(
        builder: (context, state) {
          return Sizer(
            builder: (context, orientation, deviceType) {
              return Scaffold(
                backgroundColor: const Color(0xFF1A1A2E),
                body: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF1A1A2E),
                        const Color(0xFF16213E),
                        const Color(0xFF0F3460),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: [
                      SafeArea(
                        child: Column(
                          children: [
                            // Header with time tracking info
                            Padding(
                              padding: EdgeInsets.all(4.w),
                              child: _buildTimeTrackingHeader(),
                            ),

                            // Main content area
                            Expanded(
                              child: orientation == Orientation.portrait
                                  ? Column(
                                      children: [
                                        // Staff selection (top half)
                                        Expanded(
                                          flex: 3,
                                          child: _buildStaffGrid(),
                                        ),

                                        // PIN entry (bottom half)
                                        Expanded(
                                          flex: 2,
                                          child: SingleChildScrollView(
                                            child: _buildPinEntrySection(),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Row(
                                      children: [
                                        // Staff selection (left side)
                                        Expanded(
                                          flex: 3,
                                          child: _buildStaffGrid(),
                                        ),

                                        // PIN entry (right side)
                                        Expanded(
                                          flex: 2,
                                          child: SingleChildScrollView(
                                            child: _buildPinEntrySection(),
                                          ),
                                        ),
                                      ],
                                    ),
                            ),

                            // Current time display at bottom
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(3.w),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.3),
                                border: Border(
                                  top: BorderSide(
                                    color: Colors.white.withValues(alpha: 0.1),
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: StreamBuilder<DateTime>(
                                stream: Stream.periodic(
                                  const Duration(seconds: 1),
                                  (_) => DateTime.now(),
                                ),
                                builder: (context, snapshot) {
                                  final now = snapshot.data ?? DateTime.now();
                                  return Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.access_time,
                                        color:
                                            Colors.white.withValues(alpha: 0.7),
                                        size: 4.w,
                                      ),
                                      SizedBox(width: 1.w),
                                      Flexible(
                                        child: Text(
                                          MediaQuery.of(context).size.width <
                                                  400
                                              ? DateFormat('MMM dd • HH:mm:ss')
                                                  .format(now)
                                              : DateFormat(
                                                      'EEEE, MMM dd, yyyy • HH:mm:ss')
                                                  .format(now),
                                          style: GoogleFonts.poppins(
                                            color: Colors.white
                                                .withValues(alpha: 0.9),
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Loading overlay
                      if (state.status == AttendanceStatus.loading)
                        Positioned.fill(
                          child: Container(
                            color: Colors.black.withValues(alpha: 0.7),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.all(6.w),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: widget.action == ClockAction.clockIn
                                        ? [
                                            Colors.green.shade400,
                                            Colors.green.shade600
                                          ]
                                        : [
                                            Colors.orange.shade400,
                                            Colors.orange.shade600
                                          ],
                                  ),
                                  borderRadius: BorderRadius.circular(4.w),
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          (widget.action == ClockAction.clockIn
                                                  ? Colors.green
                                                  : Colors.orange)
                                              .withValues(alpha: 0.3),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircularProgressIndicator(
                                      valueColor:
                                          const AlwaysStoppedAnimation<Color>(
                                              Colors.white),
                                      strokeWidth: 3,
                                    ),
                                    SizedBox(height: 3.h),
                                    Text(
                                      widget.action == ClockAction.clockIn
                                          ? 'Clocking In...'
                                          : 'Clocking Out...',
                                      style: GoogleFonts.poppins(
                                        color: Colors.white,
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 1.h),
                                    Text(
                                      'Please wait',
                                      style: GoogleFonts.poppins(
                                        color:
                                            Colors.white.withValues(alpha: 0.8),
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
