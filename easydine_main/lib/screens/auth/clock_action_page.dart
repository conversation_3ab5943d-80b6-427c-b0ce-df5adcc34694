import 'package:easydine_main/blocs/staff/staff_bloc.dart';
import 'package:easydine_main/blocs/staff/staff_event.dart';
import 'package:easydine_main/blocs/staff/staff_state.dart';
import 'package:easydine_main/blocs/attendance/attendance_bloc.dart';
import 'package:easydine_main/blocs/attendance/attendance_event.dart';
import 'package:easydine_main/blocs/attendance/attendance_state.dart';
import 'package:easydine_main/models/staff_model.dart';
import 'package:easydine_main/services/branch_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../widgets/tiled_background.dart';
import 'package:intl/intl.dart';

enum ClockAction { clockIn, clockOut }

class ClockActionPage extends StatefulWidget {
  final ClockAction action;

  const ClockActionPage({
    super.key,
    required this.action,
  });

  @override
  State<ClockActionPage> createState() => _ClockActionPageState();
}

class _ClockActionPageState extends State<ClockActionPage> {
  String _pin = '';
  bool _showError = false;
  StaffModel? _selectedStaff;

  void _onKeyPress(String digit) {
    if (_pin.length < 5) {
      setState(() {
        _pin += digit;
        _showError = false;
      });

      if (_pin.length == 5 && _selectedStaff != null) {
        if (widget.action == ClockAction.clockIn) {
          context.read<AttendanceBloc>().add(
                ClockInStaff(
                  pin: _pin,
                  staffId: _selectedStaff!.id,
                ),
              );
        } else {
          context.read<AttendanceBloc>().add(
                ClockOutStaff(
                  pin: _pin,
                  staffId: _selectedStaff!.id,
                ),
              );
        }
      }
    }
  }

  void _onBackspace() {
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
        _showError = false;
      });
    }
  }

  Widget _buildStaffList() {
    return BlocBuilder<StaffBloc, StaffState>(
      builder: (context, staffState) {
        // Filter staff list based on clock action
        final filteredStaffList = widget.action == ClockAction.clockOut
            ? staffState.clockedInStaff
            : staffState.allStaff;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white10,
            border: Border(
              right: BorderSide(
                color: Colors.white24,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  widget.action == ClockAction.clockOut
                      ? 'Select Clocked-In Staff'
                      : 'Select Staff',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: filteredStaffList.length,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemBuilder: (context, index) {
                    final staff = filteredStaffList[index];
                    final isSelected = _selectedStaff?.id == staff.id;

                    return Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 4,
                        horizontal: 8,
                      ),
                      child: ListTile(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        selected: isSelected,
                        selectedTileColor: Colors.transparent,
                        leading: CircleAvatar(
                          backgroundColor:
                              isSelected ? Colors.deepOrange : Colors.white24,
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                          ),
                        ),
                        title: Text(
                          staff.name,
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              staff.role ?? 'Staff',
                              style: GoogleFonts.poppins(
                                color: Colors.white70,
                              ),
                            ),
                            if (widget.action == ClockAction.clockOut)
                              Text(
                                'Currently Clocked In',
                                style: GoogleFonts.poppins(
                                  color: Colors.green,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        onTap: () {
                          setState(() {
                            _selectedStaff = staff;
                            _pin = '';
                            _showError = false;
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPinEntry() {
    final actionText =
        widget.action == ClockAction.clockIn ? 'Clock In' : 'Clock Out';
    final actionIcon = widget.action == ClockAction.clockIn
        ? Icons.login_rounded
        : Icons.logout_rounded;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            actionIcon,
            size: 48,
            color: widget.action == ClockAction.clockIn
                ? Colors.green
                : Colors.orange,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          _selectedStaff == null
              ? 'Select a Staff Member'
              : '$actionText - ${_selectedStaff!.name}',
          style: GoogleFonts.poppins(
            fontSize: 24,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 40),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            5,
            (index) => Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: index < _pin.length ? Colors.white : Colors.white24,
                ),
              ),
            ),
          ),
        ),
        if (_showError)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              'Invalid PIN',
              style: GoogleFonts.poppins(
                color: Colors.red[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        const SizedBox(height: 20),
        Container(
          constraints: const BoxConstraints(maxWidth: 300),
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: GridView.count(
            shrinkWrap: true,
            crossAxisCount: 3,
            childAspectRatio: 1,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            children: [
              for (var i = 1; i <= 9; i++) _buildKeypadButton(i.toString()),
              _buildKeypadButton(''),
              _buildKeypadButton('0'),
              _buildKeypadButton('⌫'),
            ],
          ),
        ),
        const SizedBox(height: 20),
        TextButton.icon(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
          label: Text(
            'Cancel',
            style: GoogleFonts.poppins(
              color: Colors.grey[400],
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildKeypadButton(String value) {
    return SizedBox(
      height: 85,
      width: 85,
      child: TextButton(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.all(8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: value.isEmpty ? Colors.transparent : Colors.white24,
        ),
        onPressed: _selectedStaff == null || value.isEmpty
            ? null
            : () {
                if (value == '⌫') {
                  _onBackspace();
                } else {
                  _onKeyPress(value);
                }
              },
        child: value == '⌫'
            ? Icon(
                Icons.backspace_outlined,
                color: Colors.grey[400],
                size: 20,
              )
            : Text(
                value,
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  color: Colors.grey[300],
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // Reset attendance state when entering the page
    context.read<AttendanceBloc>().add(const ResetAttendanceState());
    _fetchStaffData();
  }

  Future<void> _fetchStaffData() async {
    final branchId = await BranchService.getSelectedBranchId();
    if (branchId != null && mounted) {
      if (widget.action == ClockAction.clockOut) {
        // Fetch clocked-in staff for clock out
        context.read<StaffBloc>().add(FetchClockedInStaff(branchId: branchId));
      } else {
        // Fetch all staff for clock in
        context.read<StaffBloc>().add(FetchBranchStaff(branchId: branchId));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AttendanceBloc, AttendanceState>(
      listenWhen: (previous, current) => previous.status != current.status,
      listener: (context, state) {
        if (state.status == AttendanceStatus.success) {
          print(
              '🎉 Clock Action: Operation successful, returning to pin entry');

          // Pop the current page and return to pin entry
          context.pop();

          // Show a brief success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${_selectedStaff?.name} has ${widget.action == ClockAction.clockIn ? 'clocked in' : 'clocked out'} successfully',
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (state.status == AttendanceStatus.error) {
          setState(() {
            _pin = '';
            _showError = true;
          });

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.error ??
                    'Failed to ${widget.action == ClockAction.clockIn ? 'clock in' : 'clock out'}. Please try again.',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: BlocBuilder<AttendanceBloc, AttendanceState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                const TiledBackground(),
                Row(
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).orientation ==
                              Orientation.portrait
                          ? MediaQuery.of(context).size.width * 0.3
                          : MediaQuery.of(context).size.width * 0.25,
                      child: _buildStaffList(),
                    ),
                    Expanded(
                      child: Center(
                        child: SingleChildScrollView(
                          child: _buildPinEntry(),
                        ),
                      ),
                    ),
                  ],
                ),
                // Adding Floating Clock
                Positioned(
                  top: 24,
                  right: 24,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.1),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            StreamBuilder(
                              stream:
                                  Stream.periodic(const Duration(seconds: 1)),
                              builder: (context, snapshot) {
                                return Text(
                                  DateFormat('HH:mm:ss').format(DateTime.now()),
                                  style: GoogleFonts.poppins(
                                    fontSize: 32,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                    letterSpacing: 2,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 4),
                            Text(
                              DateFormat('EEEE, MMMM d, y')
                                  .format(DateTime.now()),
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.white70,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                //aD
                // Loading overlay
                if (state.status == AttendanceStatus.loading)
                  Container(
                    color: Colors.black54,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.deepOrange),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Processing...',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
