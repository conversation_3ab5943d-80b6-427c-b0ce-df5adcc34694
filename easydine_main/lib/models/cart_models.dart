import 'package:equatable/equatable.dart';
import 'cartItem.dart';

// Misc item model for cart
class CartMiscItem extends Equatable {
  final String miscItemId;
  final String name;
  final double price;
  final DateTime? addedAt;

  const CartMiscItem({
    required this.miscItemId,
    required this.name,
    required this.price,
    this.addedAt,
  });

  factory CartMiscItem.fromJson(Map<String, dynamic> json) => CartMiscItem(
        miscItemId: json['miscItemId'] ?? json['id'] ?? '',
        name: json['name'] ?? '',
        price: (json['price'] ?? 0).toDouble(),
        addedAt:
            json['addedAt'] != null ? DateTime.parse(json['addedAt']) : null,
      );

  Map<String, dynamic> toJson() => {
        'miscItemId': miscItemId,
        'name': name,
        'price': price,
        if (addedAt != null) 'addedAt': addedAt!.toIso8601String(),
      };

  @override
  List<Object?> get props => [miscItemId, name, price, addedAt];
}

// Alert item model for cart
class CartAlertItem extends Equatable {
  final String alertId;
  final String note;
  final DateTime? addedAt;

  const CartAlertItem({
    required this.alertId,
    required this.note,
    this.addedAt,
  });

  factory CartAlertItem.fromJson(Map<String, dynamic> json) => CartAlertItem(
        alertId: json['alertId'] ?? json['id'] ?? '',
        note: json['note'] ?? '',
        addedAt:
            json['addedAt'] != null ? DateTime.parse(json['addedAt']) : null,
      );

  Map<String, dynamic> toJson() => {
        'alertId': alertId,
        'note': note,
        if (addedAt != null) 'addedAt': addedAt!.toIso8601String(),
      };

  @override
  List<Object?> get props => [alertId, note, addedAt];
}

// Branch model for cart
class CartBranch extends Equatable {
  final String id;
  final String name;

  const CartBranch({
    required this.id,
    required this.name,
  });

  factory CartBranch.fromJson(Map<String, dynamic> json) => CartBranch(
        id: json['id'],
        name: json['name'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
      };

  @override
  List<Object> get props => [id, name];
}

// Server-side cart item model
class ServerCartItem extends Equatable {
  final String id;
  final String dishId;
  final String name; // Added name field from API
  final double price; // Added price field from API
  final int quantity;
  final String type;
  final String? notes;
  final List<String> allergyIds;
  final List<String> allergyNames; // Added to store allergy names from API
  final List<DishAddon> dishAddons;
  final List<DishExtra> dishExtras;
  final List<DishSide> dishSides;
  final List<DishBeverage> dishBeverages;
  final List<DishDessert> dishDesserts;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ServerCartItem({
    required this.id,
    required this.dishId,
    required this.name,
    required this.price,
    required this.quantity,
    required this.type,
    this.notes,
    this.allergyIds = const [],
    this.allergyNames = const [],
    this.dishAddons = const [],
    this.dishExtras = const [],
    this.dishSides = const [],
    this.dishBeverages = const [],
    this.dishDesserts = const [],
    this.createdAt,
    this.updatedAt,
  });

  factory ServerCartItem.fromJson(Map<String, dynamic> json) => ServerCartItem(
        id: json['cartItemId'], // API uses 'cartItemId' instead of 'id'
        dishId:
            json['baseItem']?['id'] ?? json['dishId'], // API has baseItem.id
        name: json['name'] ?? json['baseItem']?['name'] ?? 'Unknown Item',
        price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
        quantity: json['quantity'],
        type: json['type'],
        notes: json['notes'],
        // Handle both allergyIds (array of strings) and allergies (array of objects)
        allergyIds: json['allergies'] != null
            ? (json['allergies'] as List<dynamic>)
                .map((allergy) => allergy['id']?.toString() ?? '')
                .where((id) => id.isNotEmpty)
                .toList()
            : List<String>.from(json['allergyIds'] ?? []),
        // Extract allergy names from the allergies array
        allergyNames: json['allergies'] != null
            ? (json['allergies'] as List<dynamic>)
                .map((allergy) => allergy['name']?.toString() ?? '')
                .where((name) => name.isNotEmpty)
                .toList()
            : const [],
        dishAddons: (json['dishAddons'] as List<dynamic>?)
                ?.map((e) => DishAddon.fromJson(e))
                .toList() ??
            [],
        dishExtras: (json['dishExtras'] as List<dynamic>?)
                ?.map((e) => DishExtra.fromJson(e))
                .toList() ??
            [],
        dishSides: (json['dishSides'] as List<dynamic>?)
                ?.map((e) => DishSide.fromJson(e))
                .toList() ??
            [],
        dishBeverages: (json['dishBeverages'] as List<dynamic>?)
                ?.map((e) => DishBeverage.fromJson(e))
                .toList() ??
            [],
        dishDesserts: (json['dishDesserts'] as List<dynamic>?)
                ?.map((e) => DishDessert.fromJson(e))
                .toList() ??
            [],
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'dishId': dishId,
        'name': name,
        'price': price,
        'quantity': quantity,
        'type': type,
        'notes': notes,
        'allergyIds': allergyIds,
        'allergyNames': allergyNames,
        'dishAddons': dishAddons.map((e) => e.toJson()).toList(),
        'dishExtras': dishExtras.map((e) => e.toJson()).toList(),
        'dishSides': dishSides.map((e) => e.toJson()).toList(),
        'dishBeverages': dishBeverages.map((e) => e.toJson()).toList(),
        'dishDesserts': dishDesserts.map((e) => e.toJson()).toList(),
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  ServerCartItem copyWith({
    String? id,
    String? dishId,
    String? name,
    double? price,
    int? quantity,
    String? type,
    String? notes,
    List<String>? allergyIds,
    List<String>? allergyNames,
    List<DishAddon>? dishAddons,
    List<DishExtra>? dishExtras,
    List<DishSide>? dishSides,
    List<DishBeverage>? dishBeverages,
    List<DishDessert>? dishDesserts,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServerCartItem(
      id: id ?? this.id,
      dishId: dishId ?? this.dishId,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      allergyIds: allergyIds ?? this.allergyIds,
      allergyNames: allergyNames ?? this.allergyNames,
      dishAddons: dishAddons ?? this.dishAddons,
      dishExtras: dishExtras ?? this.dishExtras,
      dishSides: dishSides ?? this.dishSides,
      dishBeverages: dishBeverages ?? this.dishBeverages,
      dishDesserts: dishDesserts ?? this.dishDesserts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        dishId,
        name,
        price,
        quantity,
        type,
        notes,
        allergyIds,
        allergyNames,
        dishAddons,
        dishExtras,
        dishSides,
        dishBeverages,
        dishDesserts,
        createdAt,
        updatedAt
      ];
}

// Main cart model
class Cart extends Equatable {
  final String cartId;
  final String staffId;
  final String status;
  final CartBranch? branch;
  final String total;
  final String? table;
  final String? orderType;
  final Map<String, dynamic>? customerInfo;
  final String? notes;
  final List<CartMiscItem> miscItems;
  final List<CartAlertItem> alerts;
  final List<ServerCartItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Cart({
    required this.cartId,
    required this.staffId,
    required this.status,
    this.branch,
    required this.total,
    this.table,
    this.orderType,
    this.customerInfo,
    this.notes,
    this.miscItems = const [],
    this.alerts = const [],
    this.items = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => Cart(
        cartId: json['cartId'],
        staffId: json['staffId'],
        status: json['status'],
        branch:
            json['branch'] != null ? CartBranch.fromJson(json['branch']) : null,
        total: json['total'],
        table: json['table'],
        orderType: json['orderType'],
        customerInfo: json['customerInfo'],
        notes: json['notes'],
        miscItems: (json['miscItems'] as List<dynamic>?)
                ?.map((e) => CartMiscItem.fromJson(e))
                .toList() ??
            [],
        alerts: (json['alerts'] as List<dynamic>?)
                ?.map((e) => CartAlertItem.fromJson(e))
                .toList() ??
            [],
        items: (json['cartItems']
                    as List<dynamic>?) // Changed from 'items' to 'cartItems'
                ?.map((e) => ServerCartItem.fromJson(e))
                .toList() ??
            [],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
      );

  Map<String, dynamic> toJson() => {
        'cartId': cartId,
        'staffId': staffId,
        'status': status,
        'branch': branch?.toJson(),
        'total': total,
        'table': table,
        'orderType': orderType,
        'customerInfo': customerInfo,
        'notes': notes,
        'miscItems': miscItems.map((e) => e.toJson()).toList(),
        'alerts': alerts.map((e) => e.toJson()).toList(),
        'items': items.map((e) => e.toJson()).toList(),
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };

  Cart copyWith({
    String? cartId,
    String? staffId,
    String? status,
    CartBranch? branch,
    String? total,
    String? table,
    String? orderType,
    Map<String, dynamic>? customerInfo,
    String? notes,
    List<CartMiscItem>? miscItems,
    List<CartAlertItem>? alerts,
    List<ServerCartItem>? items,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Cart(
      cartId: cartId ?? this.cartId,
      staffId: staffId ?? this.staffId,
      status: status ?? this.status,
      branch: branch ?? this.branch,
      total: total ?? this.total,
      table: table ?? this.table,
      orderType: orderType ?? this.orderType,
      customerInfo: customerInfo ?? this.customerInfo,
      notes: notes ?? this.notes,
      miscItems: miscItems ?? this.miscItems,
      alerts: alerts ?? this.alerts,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        cartId,
        staffId,
        status,
        branch,
        total,
        table,
        orderType,
        customerInfo,
        notes,
        miscItems,
        alerts,
        items,
        createdAt,
        updatedAt
      ];
}

// Request models for API calls
class AddItemToCartRequest extends Equatable {
  final int quantity;
  final String dishId;
  final String type;
  final String? notes;
  final List<String> allergyIds;
  final List<DishAddon> dishAddons;
  final List<DishExtra> dishExtras;
  final List<DishSide> dishSides;
  final List<DishBeverage> dishBeverages;
  final List<DishDessert> dishDesserts;

  const AddItemToCartRequest({
    required this.quantity,
    required this.dishId,
    this.type = "customized",
    this.notes,
    this.allergyIds = const [],
    this.dishAddons = const [],
    this.dishExtras = const [],
    this.dishSides = const [],
    this.dishBeverages = const [],
    this.dishDesserts = const [],
  });

  Map<String, dynamic> toJson() => {
        'quantity': quantity,
        'dishId': dishId,
        'type': type,
        if (notes != null) 'notes': notes,
        if (allergyIds.isNotEmpty) 'allergyIds': allergyIds,
        if (dishAddons.isNotEmpty)
          'dishAddons': dishAddons.map((e) => e.toJson()).toList(),
        if (dishExtras.isNotEmpty)
          'dishExtras': dishExtras.map((e) => e.toJson()).toList(),
        if (dishSides.isNotEmpty)
          'dishSides': dishSides.map((e) => e.toJson()).toList(),
        if (dishBeverages.isNotEmpty)
          'dishBeverages': dishBeverages.map((e) => e.toJson()).toList(),
        if (dishDesserts.isNotEmpty)
          'dishDesserts': dishDesserts.map((e) => e.toJson()).toList(),
      };

  @override
  List<Object?> get props => [
        quantity,
        dishId,
        type,
        notes,
        allergyIds,
        dishAddons,
        dishExtras,
        dishSides,
        dishBeverages,
        dishDesserts
      ];
}

class UpdateItemQuantityRequest extends Equatable {
  final int quantity;

  const UpdateItemQuantityRequest({required this.quantity});

  Map<String, dynamic> toJson() => {'quantity': quantity};

  @override
  List<Object> get props => [quantity];
}

class ConfirmCartRequest extends Equatable {
  final String? assignedWaiterId;
  final String? tableId;
  final String? orderTypeId;

  const ConfirmCartRequest({
    this.assignedWaiterId,
    this.tableId,
    this.orderTypeId,
  });

  Map<String, dynamic> toJson() => {
        if (assignedWaiterId != null) 'assignedWaiterId': assignedWaiterId,
        if (tableId != null) 'tableId': tableId,
        if (orderTypeId != null) 'orderTypeId': orderTypeId,
      };

  @override
  List<Object?> get props => [assignedWaiterId, tableId, orderTypeId];
}
