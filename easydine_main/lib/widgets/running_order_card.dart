import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class RunningOrderCard extends StatelessWidget {
  final Map<String, dynamic> order;
  final VoidCallback? onLoadToCart;
  final VoidCallback? onCancelOrder;
  static final Map<String, Color> _tableColorMap =
      {}; // Stores unique colors per table
  static final List<Color> _tableColors = [
    Color(0xFF4A90E2), // Modern blue
    Color(0xFF50E3C2), // Mint
    Color(0xFF9B51E0), // Purple
    Color(0xFFFF9500), // Orange
    Color(0xFF00BFA5), // Teal
    Color(0xFFFF4081), // Pink
    Color(0xFF795548), // Brown
    Color(0xFF3F51B5), // Indigo
    Color(0xFFFF5252), // Red
    Color(0xFF00BCD4), // Cyan
  ];
  static int _colorIndex = 0;

  RunningOrderCard({
    super.key,
    required this.order,
    this.onLoadToCart,
    this.onCancelOrder,
  }) {
    // Ensure each table gets a unique color
    final String tableNumber = (order["table"] ?? "Unknown").toString();
    if (!_tableColorMap.containsKey(tableNumber)) {
      _tableColorMap[tableNumber] = _tableColors[_colorIndex];
      _colorIndex =
          (_colorIndex + 1) % _tableColors.length; // Cycle through colors
    }
  }

  Color getTableColor(String table) {
    return _tableColorMap[table] ??
        Colors.grey; // Default color if not assigned
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "ready":
        return Color(0xFF2CBF5A);
      case "in progress":
        return Color(0xFF4A90E2);
      case "cooking":
        return Color(0xFFFF9500);
      default:
        return Color(0xFF9E9E9E);
    }
  }

  bool _hasCustomizations(List<dynamic>? items) {
    if (items == null || items.isEmpty) return false;

    for (var item in items) {
      final customization = item['customization'];
      if (customization != null) {
        // Check if any customization field has content
        if ((customization['allergies'] != null &&
                (customization['allergies'] as List).isNotEmpty) ||
            (customization['spiciness'] != null &&
                customization['spiciness'].toString().isNotEmpty) ||
            (customization['dishAddons'] != null &&
                (customization['dishAddons'] as List).isNotEmpty) ||
            (customization['dishExtras'] != null &&
                (customization['dishExtras'] as List).isNotEmpty) ||
            (customization['notes'] != null &&
                customization['notes'].toString().isNotEmpty)) {
          return true;
        }
      }
    }
    return false;
  }

  String getStatusMessage(String status) {
    switch (status) {
      case "Ready":
        return "Order is ready to be served";
      case "In Progress":
        return "Order is being prepared";
      case "Cooking":
        return "Order is being cooked";
      default:
        return "Order status unknown";
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderType = order['type'] as String;

    // Route to different card layouts based on order type
    switch (orderType.toLowerCase()) {
      case 'dine-in':
      case 'dine in':
        return _buildDineInCard(context);
      case 'takeaway':
      case 'take away':
        return _buildTakeawayCard(context);
      case 'delivery':
        return _buildDeliveryCard(context);
      default:
        return _buildGenericCard(context);
    }
  }

  Widget _buildDineInCard(BuildContext context) {
    final String tableNumber = (order["table"] ?? "Unknown").toString();
    final String orderId = (order["id"] ?? "").toString();
    final status = (order["status"] ?? "Unknown").toString();
    final customer = order['customer'] as String;
    final time = order['time'] as String;
    final numberOfPeople = order['numberOfPeople'] as int? ?? 1;
    final assignedWaiter = order['assignedWaiter'] as String?;

    int itemCount = (order['items'] as List<dynamic>? ?? [])
        .map<int>((item) => (item['quantity'] ?? 0) as int)
        .fold(0, (a, b) => a + b);

    double totalAmount =
        (order['items'] as List<dynamic>? ?? []).fold(0.0, (sum, item) {
      final price = (item['price'] ?? 0.0).toDouble();
      final quantity = (item['quantity'] ?? 1).toInt();
      return sum + (price * quantity);
    });

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Order type badge
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue, width: 1.5),
                      ),
                      child: Text(
                        "DINE-IN",
                        style: GoogleFonts.dmSans(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    // Table indicator
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _tableColorMap[tableNumber]?.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _tableColorMap[tableNumber] ?? Colors.grey,
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.table_restaurant,
                              color: _tableColorMap[tableNumber], size: 14),
                          SizedBox(width: 4),
                          Text(
                            "Table $tableNumber",
                            style: GoogleFonts.dmSans(
                              color: _tableColorMap[tableNumber],
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    // Status badge
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: getStatusColor(status).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: getStatusColor(status)),
                      ),
                      child: Text(
                        status.toUpperCase(),
                        style: GoogleFonts.dmSans(
                          color: getStatusColor(status),
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),

                // Order details
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "#$orderId",
                            style: GoogleFonts.dmSans(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.people, color: Colors.green, size: 14),
                              SizedBox(width: 4),
                              Text(
                                "$numberOfPeople guest${numberOfPeople != 1 ? 's' : ''}",
                                style: GoogleFonts.dmSans(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          if (assignedWaiter != null) ...[
                            SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(Icons.person_pin,
                                    color: Colors.purple, size: 14),
                                SizedBox(width: 4),
                                Text(
                                  "Waiter: $assignedWaiter",
                                  style: GoogleFonts.dmSans(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  color: Colors.orange, size: 14),
                              SizedBox(width: 4),
                              Text(
                                time,
                                style: GoogleFonts.dmSans(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "\$${totalAmount.toStringAsFixed(2)}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          "$itemCount item${itemCount != 1 ? 's' : ''}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                        if (_hasCustomizations(order["items"])) ...[
                          SizedBox(height: 2),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.tune, size: 12, color: Colors.amber),
                              SizedBox(width: 2),
                              Text(
                                "Customized",
                                style: GoogleFonts.dmSans(
                                  color: Colors.amber,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    if (onLoadToCart == null && onCancelOrder == null) {
      return SizedBox.shrink();
    }

    return Column(
      children: [
        SizedBox(height: 12),
        Row(
          children: [
            if (onLoadToCart != null)
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: onLoadToCart,
                  icon: Icon(Icons.shopping_cart, size: 16),
                  label: Text(
                    'Load to Cart',
                    style: GoogleFonts.dmSans(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            if (onLoadToCart != null && onCancelOrder != null)
              SizedBox(width: 8),
            if (onCancelOrder != null)
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: onCancelOrder,
                  icon: Icon(Icons.cancel, size: 16),
                  label: Text(
                    'Cancel',
                    style: GoogleFonts.dmSans(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildTakeawayCard(BuildContext context) {
    final String orderId = (order["id"] ?? "").toString();
    final status = (order["status"] ?? "Unknown").toString();
    final customer = order['customer'] as String;
    final time = order['time'] as String;
    final customerInfo = order['customerInfo'] as Map<String, dynamic>?;
    final estimatedPrepTime = order['estimatedPrepTime'] as int?;
    final readyTime = order['readyTime'] as String?;

    int itemCount = (order['items'] as List<dynamic>? ?? [])
        .map<int>((item) => (item['quantity'] ?? 0) as int)
        .fold(0, (a, b) => a + b);

    double totalAmount =
        (order['items'] as List<dynamic>? ?? []).fold(0.0, (sum, item) {
      final price = (item['price'] ?? 0.0).toDouble();
      final quantity = (item['quantity'] ?? 1).toInt();
      return sum + (price * quantity);
    });

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Order type badge
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.orange, width: 1.5),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.takeout_dining,
                              color: Colors.orange, size: 14),
                          SizedBox(width: 4),
                          Text(
                            "TAKEAWAY",
                            style: GoogleFonts.dmSans(
                              color: Colors.orange,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    // Status badge
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: getStatusColor(status).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: getStatusColor(status)),
                      ),
                      child: Text(
                        status.toUpperCase(),
                        style: GoogleFonts.dmSans(
                          color: getStatusColor(status),
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),

                // Order details
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "#$orderId",
                            style: GoogleFonts.dmSans(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.person, color: Colors.green, size: 14),
                              SizedBox(width: 4),
                              Text(
                                customer,
                                style: GoogleFonts.dmSans(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          if (customerInfo?['phone'] != null) ...[
                            SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(Icons.phone, color: Colors.blue, size: 14),
                                SizedBox(width: 4),
                                Text(
                                  customerInfo!['phone'],
                                  style: GoogleFonts.dmSans(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  color: Colors.orange, size: 14),
                              SizedBox(width: 4),
                              Text(
                                "Ordered: $time",
                                style: GoogleFonts.dmSans(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          if (estimatedPrepTime != null) ...[
                            SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(Icons.timer,
                                    color: Colors.purple, size: 14),
                                SizedBox(width: 4),
                                Text(
                                  "Prep: ${estimatedPrepTime}min",
                                  style: GoogleFonts.dmSans(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          if (readyTime != null) ...[
                            SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(Icons.check_circle,
                                    color: Colors.green, size: 14),
                                SizedBox(width: 4),
                                Text(
                                  "Ready: $readyTime",
                                  style: GoogleFonts.dmSans(
                                    color: Colors.green,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "\$${totalAmount.toStringAsFixed(2)}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          "$itemCount item${itemCount != 1 ? 's' : ''}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                        if (_hasCustomizations(order["items"])) ...[
                          SizedBox(height: 2),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.tune, size: 12, color: Colors.amber),
                              SizedBox(width: 2),
                              Text(
                                "Customized",
                                style: GoogleFonts.dmSans(
                                  color: Colors.amber,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeliveryCard(BuildContext context) {
    final String orderId = (order["id"] ?? "").toString();
    final status = (order["status"] ?? "Unknown").toString();
    final customer = order['customer'] as String;
    final time = order['time'] as String;
    final customerInfo = order['customerInfo'] as Map<String, dynamic>?;
    final estimatedPrepTime = order['estimatedPrepTime'] as int?;

    int itemCount = (order['items'] as List<dynamic>? ?? [])
        .map<int>((item) => (item['quantity'] ?? 0) as int)
        .fold(0, (a, b) => a + b);

    double totalAmount =
        (order['items'] as List<dynamic>? ?? []).fold(0.0, (sum, item) {
      final price = (item['price'] ?? 0.0).toDouble();
      final quantity = (item['quantity'] ?? 1).toInt();
      return sum + (price * quantity);
    });

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Order type badge
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.red, width: 1.5),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.delivery_dining,
                              color: Colors.red, size: 14),
                          SizedBox(width: 4),
                          Text(
                            "DELIVERY",
                            style: GoogleFonts.dmSans(
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    // Status badge
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: getStatusColor(status).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: getStatusColor(status)),
                      ),
                      child: Text(
                        status.toUpperCase(),
                        style: GoogleFonts.dmSans(
                          color: getStatusColor(status),
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),

                // Order details
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "#$orderId",
                            style: GoogleFonts.dmSans(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.person, color: Colors.green, size: 14),
                              SizedBox(width: 4),
                              Text(
                                customer,
                                style: GoogleFonts.dmSans(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          if (customerInfo?['phone'] != null) ...[
                            SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(Icons.phone, color: Colors.blue, size: 14),
                                SizedBox(width: 4),
                                Text(
                                  customerInfo!['phone'],
                                  style: GoogleFonts.dmSans(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  color: Colors.orange, size: 14),
                              SizedBox(width: 4),
                              Text(
                                "Ordered: $time",
                                style: GoogleFonts.dmSans(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          if (estimatedPrepTime != null) ...[
                            SizedBox(height: 2),
                            Row(
                              children: [
                                Icon(Icons.timer,
                                    color: Colors.purple, size: 14),
                                SizedBox(width: 4),
                                Text(
                                  "Prep + Delivery: ${estimatedPrepTime}min",
                                  style: GoogleFonts.dmSans(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "\$${totalAmount.toStringAsFixed(2)}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          "$itemCount item${itemCount != 1 ? 's' : ''}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                        if (_hasCustomizations(order["items"])) ...[
                          SizedBox(height: 2),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.tune, size: 12, color: Colors.amber),
                              SizedBox(width: 2),
                              Text(
                                "Customized",
                                style: GoogleFonts.dmSans(
                                  color: Colors.amber,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGenericCard(BuildContext context) {
    final String orderId = (order["id"] ?? "").toString();
    final status = (order["status"] ?? "Unknown").toString();
    final orderType = order['type'] as String;
    final customer = order['customer'] as String;
    final time = order['time'] as String;

    int itemCount = (order['items'] as List<dynamic>? ?? [])
        .map<int>((item) => (item['quantity'] ?? 0) as int)
        .fold(0, (a, b) => a + b);

    double totalAmount =
        (order['items'] as List<dynamic>? ?? []).fold(0.0, (sum, item) {
      final price = (item['price'] ?? 0.0).toDouble();
      final quantity = (item['quantity'] ?? 1).toInt();
      return sum + (price * quantity);
    });

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Order type badge
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey, width: 1.5),
                      ),
                      child: Text(
                        orderType.toUpperCase(),
                        style: GoogleFonts.dmSans(
                          color: Colors.grey,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    Spacer(),
                    // Status badge
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: getStatusColor(status).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: getStatusColor(status)),
                      ),
                      child: Text(
                        status.toUpperCase(),
                        style: GoogleFonts.dmSans(
                          color: getStatusColor(status),
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),

                // Order details
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "#$orderId",
                            style: GoogleFonts.dmSans(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.person, color: Colors.green, size: 14),
                              SizedBox(width: 4),
                              Text(
                                customer,
                                style: GoogleFonts.dmSans(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  color: Colors.orange, size: 14),
                              SizedBox(width: 4),
                              Text(
                                time,
                                style: GoogleFonts.dmSans(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "\$${totalAmount.toStringAsFixed(2)}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          "$itemCount item${itemCount != 1 ? 's' : ''}",
                          style: GoogleFonts.dmSans(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                        if (_hasCustomizations(order["items"])) ...[
                          SizedBox(height: 2),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.tune, size: 12, color: Colors.amber),
                              SizedBox(width: 2),
                              Text(
                                "Customized",
                                style: GoogleFonts.dmSans(
                                  color: Colors.amber,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
