// lib/widgets/menu_grid.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sizer/sizer.dart';
import '../blocs/pos/pos_state.dart';
import '../blocs/pos/pos_bloc.dart';
import '../models/menuItem.dart';
import '../models/menu_response_models.dart';
import 'menu_item_card.dart';
import '../services/menu_service.dart';
import '../config/api_config.dart';

class MenuGrid extends StatefulWidget {
  const MenuGrid({super.key});

  @override
  State<MenuGrid> createState() => _MenuGridState();
}

class _MenuGridState extends State<MenuGrid> {
  MenuWithAvailability? _menuData;
  bool _isLoading = true;
  String? _error;
  List<MenuItem> _legacyMenuItems = [];
  Map<String, List<MenuItem>> _groupedItems = {};
  bool _showMenuAnyway = false; // Flag to show menu even when unavailable

  // Key for storing selected branch ID in SharedPreferences
  static const String _selectedBranchIdKey = 'selected_branch_id';

  @override
  void initState() {
    super.initState();
    _loadMenuData();
  }

  @override
  void dispose() {
    // Don't clear cache on dispose as other widgets might need it
    super.dispose();
  }

  // Public method to refresh menu data
  Future<void> refreshMenu() async {
    setState(() {
      _showMenuAnyway = false; // Reset the flag when refreshing
    });
    await _loadMenuData();
  }

  // Public method to show menu anyway (can be called from parent widgets)
  void showMenuAnyway() {
    if (!_showMenuAnyway) {
      setState(() {
        _showMenuAnyway = true;
        _error = null;
        _isLoading = true;
      });
      _loadMenuData();
    }
  }

  // Public method to hide menu when unavailable (reset to normal behavior)
  void hideUnavailableMenu() {
    if (_showMenuAnyway) {
      setState(() {
        _showMenuAnyway = false;
      });
      _loadMenuData();
    }
  }

  // Helper method to get branch ID from SharedPreferences
  Future<String> _getBranchId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? selectedBranchId = prefs.getString(_selectedBranchIdKey);

      if (selectedBranchId != null && selectedBranchId.isNotEmpty) {
        return selectedBranchId;
      }

      // Fall back to default branch ID
      return ApiConfig.defaultBranchId;
    } catch (e) {
      // If there's any error accessing SharedPreferences, use default
      return ApiConfig.defaultBranchId;
    }
  }

  // Helper method to get user-friendly unavailability message
  String _getUnavailabilityMessage(String? reason) {
    switch (reason?.toLowerCase()) {
      case 'outside_operating_hours':
        return 'Menu is currently unavailable.\nWe are outside operating hours.';
      case 'temporarily_closed':
        return 'Menu is temporarily unavailable.\nThe restaurant is temporarily closed.';
      case 'maintenance':
        return 'Menu is temporarily unavailable.\nSystem is under maintenance.';
      case 'no_delivery':
        return 'Delivery is currently unavailable.\nPlease try pickup or dine-in options.';
      case 'no_pickup':
        return 'Pickup is currently unavailable.\nPlease try delivery or dine-in options.';
      case 'kitchen_closed':
        return 'Menu is currently unavailable.\nKitchen is closed.';
      case 'staff_shortage':
        return 'Menu is temporarily unavailable.\nLimited staff available.';
      case 'holiday':
        return 'Menu is currently unavailable.\nWe are closed for the holiday.';
      case 'special_event':
        return 'Menu is temporarily unavailable.\nSpecial event in progress.';
      default:
        return reason != null && reason.isNotEmpty
            ? 'Menu is currently unavailable.\nReason: ${reason.replaceAll('_', ' ')}'
            : 'Menu is currently unavailable.\nPlease try again later.';
    }
  }

  // Helper method to get short unavailability reason for banner
  String _getShortUnavailabilityReason(String? reason) {
    switch (reason?.toLowerCase()) {
      case 'outside_operating_hours':
        return 'outside operating hours';
      case 'temporarily_closed':
        return 'temporarily closed';
      case 'maintenance':
        return 'under maintenance';
      case 'no_delivery':
        return 'delivery unavailable';
      case 'no_pickup':
        return 'pickup unavailable';
      case 'kitchen_closed':
        return 'kitchen closed';
      case 'staff_shortage':
        return 'limited staff';
      case 'holiday':
        return 'closed for holiday';
      case 'special_event':
        return 'special event';
      default:
        return reason?.replaceAll('_', ' ') ?? 'temporarily unavailable';
    }
  }

  Future<void> _loadMenuData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Check if menu data is already cached
      if (MenuService.getAllCategories().isNotEmpty) {
        debugPrint('🍽️ MenuGrid: Menu data already cached, skipping API call');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Get branch ID from SharedPreferences with fallback
      final String branchId = await _getBranchId();
      debugPrint('🍽️ MenuGrid: Loading menu for branch ID: $branchId');

      final menuData = await MenuService.getActiveMenuWithAvailability(
        branchId: branchId,
        forPOS: true,
      );

      if (menuData != null) {
        // Check if the menu is available at the menu level
        if (!menuData.availabilityInfo.isAvailable && !_showMenuAnyway) {
          setState(() {
            _menuData = menuData;
            _error =
                _getUnavailabilityMessage(menuData.availabilityInfo.reason);
            _isLoading = false;
          });
          return;
        }

        // Convert API data to legacy MenuItem format for compatibility
        final availableDishes = MenuService.getAvailableDishes(menuData);

        // Convert to legacy format
        final legacyItems =
            MenuService.convertDishesToMenuItems(availableDishes);

        // Group items by section name (using as category)
        final Map<String, List<MenuItem>> grouped = {};
        for (var section in menuData.sections) {
          // Check if section is available (unless we're showing menu anyway)
          if (!section.availabilityInfo.isAvailable && !_showMenuAnyway) {
            // Skip unavailable sections but log the reason
            debugPrint(
                'Section "${section.name}" is unavailable: ${section.availabilityInfo.reason}');
            continue;
          }

          if (section.dishes.isNotEmpty) {
            final sectionDishes = _showMenuAnyway
                ? section.dishes // Show all dishes when flag is true
                : section.dishes.where((dish) => dish.isAvailable).toList();
            if (sectionDishes.isNotEmpty) {
              final sectionMenuItems =
                  MenuService.convertDishesToMenuItems(sectionDishes);
              // Update category to section name
              final updatedItems = sectionMenuItems
                  .map((item) => MenuItem(
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        image: item.image,
                        category: section.name, // Use section name as category
                        description: item.description,
                        ingredients: item.ingredients,
                        prepTime: item.prepTime,
                        rating: item.rating,
                        isSpicy: item.isSpicy,
                        dietaryInfo: item.dietaryInfo,
                      ))
                  .toList();
              grouped[section.name] = updatedItems;
            }
          }
        }

        // Check if no sections are available
        if (grouped.isEmpty) {
          setState(() {
            _menuData = menuData;
            _error =
                'No menu sections are currently available.\nAll sections are temporarily unavailable.';
            _isLoading = false;
          });
          return;
        }

        // Update the MenuService cache so other widgets can access the data
        MenuService.updateCache(menuData, grouped);

        // Debug: Log some dish IDs for verification
        final allDishes = MenuService.getAllDishesFromMenu(menuData);
        debugPrint('🍽️ MenuGrid: Loaded ${allDishes.length} dishes from menu');
        if (allDishes.isNotEmpty) {
          debugPrint('🍽️ MenuGrid: First few dish IDs:');
          for (int i = 0;
              i < (allDishes.length > 5 ? 5 : allDishes.length);
              i++) {
            debugPrint(
                '🍽️ MenuGrid: - ${allDishes[i].dishId}: ${allDishes[i].name}');
          }
        }

        setState(() {
          _menuData = menuData;
          _legacyMenuItems = legacyItems;
          _groupedItems = grouped;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Failed to load menu data';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading menu: $e';
        _isLoading = false;
      });
    }
  }

  List<MenuItem> _getFilteredItems(POSState state) {
    if (_groupedItems.isEmpty) return [];

    if (state.searchQuery.isNotEmpty) {
      // Search across all items
      final allItems = _groupedItems.values.expand((items) => items).toList();
      return allItems
          .where((item) =>
              item.name
                  .toLowerCase()
                  .contains(state.searchQuery.toLowerCase()) ||
              item.description
                  .toLowerCase()
                  .contains(state.searchQuery.toLowerCase()))
          .toList();
    } else if (state.selectedCategory == "All") {
      // Return all items
      return _groupedItems.values.expand((items) => items).toList();
    } else {
      // Return items for specific category
      return _groupedItems[state.selectedCategory] ?? [];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading state
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    // Show error state
    if (_error != null) {
      final bool isUnavailabilityError = _error!.contains('unavailable');

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isUnavailabilityError ? Icons.schedule : Icons.error_outline,
              color: Colors.white.withValues(alpha: 0.7),
              size: 12.sp,
            ),
            SizedBox(height: 2.h),
            Text(
              isUnavailabilityError
                  ? 'Menu Unavailable'
                  : 'Failed to load menu',
              style: GoogleFonts.poppins(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 4.5.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                _error!,
                style: GoogleFonts.poppins(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 3.5.sp,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 1.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _loadMenuData,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isUnavailabilityError ? Colors.orange : Colors.blue,
                    foregroundColor: Colors.white,
                    padding:
                        EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.w),
                  ),
                  child: Text(isUnavailabilityError ? 'Check Again' : 'Retry'),
                ),
                if (isUnavailabilityError && _menuData != null) ...[
                  SizedBox(width: 3.w),
                  OutlinedButton(
                    onPressed: _showMenuAnyway
                        ? null
                        : () {
                            setState(() {
                              _showMenuAnyway = true;
                              _error = null;
                              _isLoading = true;
                            });
                            _loadMenuData();
                          },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                      padding:
                          EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.w),
                    ),
                    child: const Text('Show Menu'),
                  ),
                ],
              ],
            ),
            if (isUnavailabilityError) ...[
              SizedBox(height: 1.h),
              Text(
                _showMenuAnyway
                    ? 'Showing menu despite availability restrictions'
                    : 'Pull down to refresh',
                style: GoogleFonts.poppins(
                  color: Colors.white.withValues(alpha: 0.4),
                  fontSize: 3.sp,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: refreshMenu,
      color: Colors.white,
      backgroundColor: Colors.black54,
      child: Column(
        children: [
          // Show availability warning banner when menu is shown despite restrictions
          if (_showMenuAnyway &&
              _menuData != null &&
              !_menuData!.availabilityInfo.isAvailable)
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.w),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.2),
                border: Border(
                  bottom: BorderSide(
                    color: Colors.orange.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.orange,
                    size: 5.sp,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      'Menu shown for browsing only - ${_getShortUnavailabilityReason(_menuData!.availabilityInfo.reason)}',
                      style: GoogleFonts.poppins(
                        color: Colors.orange,
                        fontSize: 4.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _showMenuAnyway = false;
                        _error = _getUnavailabilityMessage(
                            _menuData!.availabilityInfo.reason);
                      });
                    },
                    icon: const Icon(
                      Icons.close,
                      color: Colors.orange,
                      size: 18,
                    ),
                    constraints: const BoxConstraints(),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
          Expanded(
            child: BlocBuilder<POSBloc, POSState>(
              builder: (context, state) {
                // Handle "All" category with grouped display
                if (state.selectedCategory == "All" &&
                    state.searchQuery.isEmpty) {
                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _groupedItems.length,
                    itemBuilder: (context, index) {
                      final category = _groupedItems.keys.elementAt(index);
                      final categoryItems = _groupedItems[category]!;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.fromLTRB(4.w, 0, 4.w, 0),
                            child: Text(
                              category,
                              style: GoogleFonts.poppins(
                                fontSize: 5.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          MasonryGridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.symmetric(horizontal: 2.w),
                            crossAxisCount:
                                MediaQuery.of(context).orientation ==
                                        Orientation.portrait
                                    ? 4
                                    : 5,
                            mainAxisSpacing: 2.w,
                            crossAxisSpacing: 2.w,
                            itemCount: categoryItems.length,
                            itemBuilder: (context, i) =>
                                MenuItemCard(item: categoryItems[i]),
                          ),
                          SizedBox(height: 1.w),
                        ],
                      );
                    },
                  );
                }

                // Get filtered items based on current state
                final menuItems = _getFilteredItems(state);

                // Show no items found message
                if (menuItems.isEmpty) {
                  return Center(
                    child: Text(
                      state.searchQuery.isNotEmpty
                          ? 'No items found for "${state.searchQuery}"'
                          : 'No items available',
                      style: GoogleFonts.poppins(
                        color: Colors.white.withValues(alpha: 0.5),
                        fontSize: 4.sp,
                      ),
                    ),
                  );
                }

                // Regular masonry grid view for single category or search results
                return MasonryGridView.count(
                  padding: EdgeInsets.all(4.w),
                  crossAxisCount:
                      MediaQuery.of(context).orientation == Orientation.portrait
                          ? 4
                          : 5,
                  mainAxisSpacing: 4.w,
                  crossAxisSpacing: 4.w,
                  itemCount: menuItems.length,
                  itemBuilder: (context, index) =>
                      MenuItemCard(item: menuItems[index]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
