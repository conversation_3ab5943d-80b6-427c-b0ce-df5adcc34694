import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'running_orders_event.dart';
import 'running_orders_state.dart';
import '../../services/order_service.dart';

class RunningOrdersBloc extends Bloc<RunningOrdersEvent, RunningOrdersState> {
  RunningOrdersBloc() : super(const RunningOrdersState()) {
    on<FetchRunningOrders>(_onFetchRunningOrders);
    on<FilterByCategory>(_onFilterByCategory);
    on<FilterByStatus>(_onFilterByStatus);
    on<SortOrders>(_onSortOrders);
    on<RefreshOrders>(_onRefreshOrders);
    on<UpdateOrderStatus>(_onUpdateOrderStatus);
  }

  // Sample data - replace with actual API call in production
  final List<Map<String, dynamic>> _sampleOrders = [
    {
      "id": "1001",
      "table": "T6",
      "status": "Ready",
      "type": "Dine-in",
      "items": [
        {"name": "Burger", "quantity": 2, "price": 12.99, "id": "1"},
        {"name": "Fries", "quantity": 1, "price": 4.99, "id": "2"},
        {"name": "Soda", "quantity": 3, "price": 1.99, "id": "3"},
      ],
      "time": "12:30 PM",
      "customer": "John Doe"
    },
    {
      "id": "1002",
      "table": "T2",
      "status": "In Progress",
      "type": "Dine-in",
      "items": [
        {"name": "Pizza", "quantity": 1, "price": 14.99, "id": "4"},
        {"name": "Wings", "quantity": 2, "price": 8.99, "id": "5"},
        {"name": "Salad", "quantity": 1, "price": 6.99, "id": "6"},
      ],
      "time": "12:35 PM",
      "customer": "Jane Smith"
    },
    {
      "id": "1003",
      "table": "D1",
      "status": "Cooking",
      "type": "Delivery",
      "items": [
        {"name": "Pasta", "quantity": 1, "price": 11.99, "id": "7"},
        {"name": "Garlic Bread", "quantity": 1, "price": 5.99, "id": "8"},
        {"name": "Garlic Bread", "quantity": 1, "price": 5.99, "id": "9"},
      ],
      "time": "12:40 PM",
      "customer": "Alice Johnson"
    },
    {
      "id": "1004",
      "table": null,
      "status": "Ready",
      "type": "Takeaway",
      "items": [
        {"name": "Sandwich", "quantity": 2, "price": 8.99, "id": "10"},
        {"name": "Coffee", "quantity": 1, "price": 3.99, "id": "11"},
      ],
      "time": "12:45 PM",
      "customer": "Bob Wilson",
      "customerInfo": {"name": "Bob Wilson", "phone": "+1234567890"}
    },
    {
      "id": "1005",
      "table": null,
      "status": "In Progress",
      "type": "Takeaway",
      "items": [
        {"name": "Chicken Wrap", "quantity": 1, "price": 9.99, "id": "12"},
        {"name": "Smoothie", "quantity": 2, "price": 5.99, "id": "13"},
      ],
      "time": "12:50 PM",
      "customer": "Sarah Davis",
      "customerInfo": {"name": "Sarah Davis", "phone": "+1987654321"},
      "estimatedPrepTime": 15
    },
  ];

  Future<void> _onFetchRunningOrders(
    FetchRunningOrders event,
    Emitter<RunningOrdersState> emit,
  ) async {
    emit(state.copyWith(status: RunningOrdersStatus.loading));

    try {
      debugPrint('🍽️ RunningOrdersBloc: Fetching orders from API...');

      // Fetch orders from the API
      final ordersResponse = await OrderService.getAllOrders(
        limit: 50, // Get more orders for better display
      );

      if (ordersResponse != null && ordersResponse.success) {
        debugPrint(
            '🍽️ RunningOrdersBloc: Successfully fetched ${ordersResponse.data.data.length} orders');

        // Convert API response to legacy format for compatibility
        final legacyOrders = ordersResponse.data.data
            .map((orderDetail) =>
                OrderService.convertToLegacyFormat(orderDetail))
            .toList();

        emit(state.copyWith(
          orders: legacyOrders,
          allOrders: legacyOrders, // Store original orders
          status: RunningOrdersStatus.success,
        ));
      } else {
        debugPrint(
            '🍽️ RunningOrdersBloc: API returned null or unsuccessful response');
        // Fallback to sample data if API fails
        emit(state.copyWith(
          orders: _sampleOrders,
          allOrders: _sampleOrders, // Store original sample orders
          status: RunningOrdersStatus.success,
        ));
      }
    } catch (e) {
      debugPrint('🍽️ RunningOrdersBloc: Error fetching orders: $e');
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Failed to fetch orders: $e',
      ));
    }
  }

  void _onFilterByCategory(
    FilterByCategory event,
    Emitter<RunningOrdersState> emit,
  ) {
    // Use allOrders for filtering to maintain original data
    final allOrders =
        state.allOrders.isNotEmpty ? state.allOrders : _sampleOrders;

    debugPrint('🍽️ RunningOrdersBloc: Filtering by ${event.category}');
    debugPrint(
        '🍽️ RunningOrdersBloc: Total orders available: ${allOrders.length}');

    // Debug order types
    final orderTypes = allOrders.map((order) => order["type"]).toSet();
    debugPrint('🍽️ RunningOrdersBloc: Available order types: $orderTypes');

    final filteredOrders = event.category == "All"
        ? allOrders
        : allOrders.where((order) => order["type"] == event.category).toList();

    debugPrint(
        '🍽️ RunningOrdersBloc: Filtering by ${event.category}, found ${filteredOrders.length} orders');

    emit(state.copyWith(
      orders: filteredOrders,
      selectedCategory: event.category,
    ));
  }

  void _onFilterByStatus(
    FilterByStatus event,
    Emitter<RunningOrdersState> emit,
  ) {
    // Use allOrders for filtering to maintain original data
    final allOrders =
        state.allOrders.isNotEmpty ? state.allOrders : _sampleOrders;

    final filteredOrders = event.statuses.isEmpty
        ? allOrders
        : allOrders
            .where((order) => event.statuses.contains(order["status"]))
            .toList();

    emit(state.copyWith(
      orders: filteredOrders,
      selectedStatuses: event.statuses,
    ));
  }

  void _onSortOrders(
    SortOrders event,
    Emitter<RunningOrdersState> emit,
  ) {
    final sortedOrders = List<Map<String, dynamic>>.from(state.orders);

    switch (event.sortBy) {
      case "newest":
        sortedOrders.sort((a, b) => b["time"].compareTo(a["time"]));
        break;
      case "oldest":
        sortedOrders.sort((a, b) => a["time"].compareTo(b["time"]));
        break;
      case "table":
        sortedOrders.sort((a, b) => a["table"].compareTo(b["table"]));
        break;
    }

    emit(state.copyWith(
      orders: sortedOrders,
      sortBy: event.sortBy,
    ));
  }

  Future<void> _onRefreshOrders(
    RefreshOrders event,
    Emitter<RunningOrdersState> emit,
  ) async {
    emit(state.copyWith(status: RunningOrdersStatus.loading));

    try {
      debugPrint('🍽️ RunningOrdersBloc: Refreshing orders from API...');

      // Fetch fresh orders from the API
      final ordersResponse = await OrderService.getAllOrders(
        limit: 50, // Get more orders for better display
      );

      if (ordersResponse != null && ordersResponse.success) {
        debugPrint(
            '🍽️ RunningOrdersBloc: Successfully refreshed ${ordersResponse.data.data.length} orders');

        // Convert API response to legacy format for compatibility
        final legacyOrders = ordersResponse.data.data
            .map((orderDetail) =>
                OrderService.convertToLegacyFormat(orderDetail))
            .toList();

        emit(state.copyWith(
          orders: legacyOrders,
          allOrders: legacyOrders, // Update allOrders during refresh
          status: RunningOrdersStatus.success,
        ));
      } else {
        debugPrint(
            '🍽️ RunningOrdersBloc: API returned null or unsuccessful response during refresh');
        // Fallback to sample data if API fails
        emit(state.copyWith(
          orders: _sampleOrders,
          allOrders: _sampleOrders, // Update allOrders during refresh fallback
          status: RunningOrdersStatus.success,
        ));
      }
    } catch (e) {
      debugPrint('🍽️ RunningOrdersBloc: Error refreshing orders: $e');
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Failed to refresh orders: $e',
      ));
    }
  }

  Future<void> _onUpdateOrderStatus(
    UpdateOrderStatus event,
    Emitter<RunningOrdersState> emit,
  ) async {
    try {
      debugPrint(
          '🍽️ RunningOrdersBloc: Updating order status for ${event.orderDetailId} to ${event.status}');

      final success = await OrderService.updateOrderStatus(
          event.orderDetailId, event.status);

      if (success) {
        debugPrint('🍽️ RunningOrdersBloc: Successfully updated order status');
        // Refresh the orders list to reflect the changes
        add(RefreshOrders());
      } else {
        debugPrint('🍽️ RunningOrdersBloc: Failed to update order status');
        emit(state.copyWith(
          status: RunningOrdersStatus.failure,
          error: 'Failed to update order status',
        ));
      }
    } catch (e) {
      debugPrint('🍽️ RunningOrdersBloc: Error updating order status: $e');
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Error updating order status: $e',
      ));
    }
  }

  Map<String, dynamic>? getOrderForTable(String tableNumber) {
    // Get the current orders (either from API or sample data)
    final currentOrders =
        state.orders.isNotEmpty ? state.orders : _sampleOrders;

    try {
      return currentOrders.firstWhere(
        (order) =>
            order["table"] == tableNumber &&
            order["status"] != "Completed" &&
            order["status"] != "Served",
        orElse: () => {},
      );
    } catch (e) {
      debugPrint(
          '🍽️ RunningOrdersBloc: No active order found for table $tableNumber');
      return null;
    }
  }
}
